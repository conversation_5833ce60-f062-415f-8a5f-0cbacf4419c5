import {
  Paragraph,
  Run,
  ImageRun,
  Table,
  TableRow,
  TableCell,
  HeadingLevel,
  AlignmentType,
} from 'docx';
import { StyleParser } from './style-parser';
import { TextProcessor } from './text-processor';
import { ImageProcessor } from './image-processor';
import { SpaceProcessor } from './space-processor';
import { LineBreakProcessor } from './line-break-processor';
import sharp = require('sharp');

export class HtmlParser {
  private readonly NODE_TYPES = {
    ELEMENT_NODE: 1,
    TEXT_NODE: 3,
  };

  private readonly spaceProcessor = new SpaceProcessor();

  /**
   * 将字符串对齐方式转换为docx的AlignmentType
   */
  private convertAlignment(alignment: string): any {
    switch (alignment) {
      case 'left':
        return AlignmentType.LEFT;
      case 'center':
        return AlignmentType.CENTER;
      case 'right':
        return AlignmentType.RIGHT;
      case 'justify':
        return AlignmentType.JUSTIFIED;
      default:
        return undefined;
    }
  }

  /**
   * 解析HTML节点
   */
  async parseHtmlNodes(
    nodeList: NodeList,
    margins?: { left?: number; right?: number }
  ): Promise<(Paragraph | Table)[]> {
    const result: (Paragraph | Table)[] = [];
    let currentParagraphRuns: Run[] = [];
    let currentParagraphAlignment: any = 'left';

    // 计算页面可用宽度
    const pageWidth = this.calculatePageWidth(margins || {});

    // Helper function to finalize the current paragraph
    const finalizeCurrentParagraph = () => {
      if (currentParagraphRuns.length > 0) {
        const paragraph = new Paragraph({
          children: currentParagraphRuns,
          alignment: currentParagraphAlignment,
          spacing: { line: 360, after: 0 }, // 减少段落后间距
        });
        result.push(paragraph);
        currentParagraphRuns = [];
        currentParagraphAlignment = 'left';
      }
    };

    for (const node of Array.from(nodeList)) {
      if (node.nodeType === this.NODE_TYPES.ELEMENT_NODE) {
        const el = node as Element;

        const parentElement = el.parentElement;
        const parentStyle = parentElement
          ? TextProcessor.extractTextStyle(parentElement)
          : {};
        const style = StyleParser.parseStyle(el);

        // 检查visibility样式，跳过隐藏的元素
        if (style.visibility === 'hidden') {
          continue;
        }

        // TODO: 忽略绝对定位的元素，这些元素在浏览器中"漂浮"，不影响正常布局
        // 后期可根据需要决定是否要处理这些元素的定位
        if (style.position === 'absolute') {
          continue; // 跳过绝对定位的元素
        }

        // 使用LineBreakProcessor进行统一的元素类型判断
        const elementType = LineBreakProcessor.getActualElementType(el);
        const shouldTreatAsParagraph =
          LineBreakProcessor.shouldTreatAsParagraph(el);
        const shouldRecurse = LineBreakProcessor.shouldRecurse(el);

        // 简化的处理逻辑 - 基于LineBreakProcessor的判断

        if (shouldTreatAsParagraph) {
          // 作为段落处理
          finalizeCurrentParagraph();

          if (el.tagName === 'P') {
            result.push(await this.parseParagraph(el));
          } else if (el.tagName === 'TABLE') {
            result.push(await this.parseTable(el));
          } else if (el.tagName.match(/^H[1-6]$/)) {
            const headingParagraph = await this.parseHeading(el);
            result.push(headingParagraph);
          } else if (el.tagName === 'UL' || el.tagName === 'OL') {
            const numberingReference =
              el.tagName === 'UL' ? 'my-unordered-list' : 'my-ordered-list';
            const listParagraphs = await this.parseList(el, numberingReference);
            result.push(...listParagraphs);
          } else {
            // DIV等其他块级元素作为段落处理
            const runs = await this.parseRuns(
              el.childNodes,
              TextProcessor.extractTextStyle(el, parentStyle),
              pageWidth
            );
            if (runs.length > 0) {
              const combinedStyle = TextProcessor.extractTextStyle(
                el,
                parentStyle
              );
              let paragraphAlignment: any = 'left';
              if (
                combinedStyle.alignment &&
                ['left', 'center', 'right', 'justify'].includes(
                  combinedStyle.alignment
                )
              ) {
                paragraphAlignment = combinedStyle.alignment;
              }

              // 检查父元素的对齐方式
              if (!paragraphAlignment || paragraphAlignment === 'left') {
                let currentEl = el.parentElement;
                while (currentEl) {
                  const parentStyle = StyleParser.parseStyle(currentEl);
                  if (
                    parentStyle.textAlign &&
                    ['left', 'center', 'right', 'justify'].includes(
                      parentStyle.textAlign
                    )
                  ) {
                    paragraphAlignment = parentStyle.textAlign;
                    break;
                  }
                  currentEl = currentEl.parentElement;
                }
              }

              result.push(
                new Paragraph({
                  children: runs,
                  alignment: paragraphAlignment,
                  spacing: { line: 360, after: 0 }, // 减少段落后间距
                })
              );
            }
          }
        } else if (shouldRecurse) {
          // 需要递归处理
          finalizeCurrentParagraph();
          const children = await this.parseHtmlNodes(el.childNodes, margins);
          result.push(...children);
        } else if (elementType === 'block' || elementType === 'inline-block') {
          // 检查inline-block元素是否包含多个块级元素
          const blockChildren = Array.from(el.children).filter(child => {
            const childType = LineBreakProcessor.getActualElementType(
              child as Element
            );
            return childType === 'block';
          });

          if (blockChildren.length > 1) {
            // 处理第一个块级元素（不添加前置换行）
            if (blockChildren.length > 0) {
              const firstBlock = blockChildren[0] as Element;

              if (firstBlock.tagName === 'P') {
                const paragraph = await this.parseParagraph(firstBlock);
                const runs = (paragraph as any)._children || [];
                currentParagraphRuns.push(...runs);
              } else if (firstBlock.tagName.match(/^H[1-6]$/)) {
                // H标签应该作为独立的段落处理，而不是作为Run
                const headingParagraph = await this.parseHeading(firstBlock);
                result.push(headingParagraph);
                // 不添加到currentParagraphRuns，因为它是独立的段落
              } else if (firstBlock.tagName === 'DIV') {
                // 递归处理DIV子元素
                const childResults = await this.parseHtmlNodes(
                  firstBlock.childNodes
                );
                result.push(...childResults);
              }
            }

            // 处理中间的块级元素（添加换行）
            for (let i = 1; i < blockChildren.length; i++) {
              finalizeCurrentParagraph();

              const blockChild = blockChildren[i] as Element;
              if (blockChild.tagName === 'P') {
                result.push(await this.parseParagraph(blockChild));
              } else if (blockChild.tagName.match(/^H[1-6]$/)) {
                const headingParagraph = await this.parseHeading(blockChild);
                result.push(headingParagraph);
              } else if (blockChild.tagName === 'DIV') {
                // 递归处理DIV子元素
                const childResults = await this.parseHtmlNodes(
                  blockChild.childNodes
                );
                result.push(...childResults);
              }
            }
          } else {
            // 如果有单个块级元素且是H标签，检查是否在"序号+内容"结构中
            if (
              blockChildren.length === 1 &&
              blockChildren[0].tagName.match(/^H[1-6]$/)
            ) {
              const headingElement = blockChildren[0] as Element;

              // 检查父元素是否是"序号+内容"结构
              const isInSequenceStructure =
                el.parentElement &&
                LineBreakProcessor.isSequenceNumberWithInlineBlockContent(
                  el.parentElement
                );

              if (isInSequenceStructure) {
                // 在"序号+内容"结构中，H标签应该保持在同一行，不作为独立段落
                const styleForRuns = TextProcessor.extractTextStyle(
                  el,
                  parentStyle
                );
                const childRuns = await this.parseRuns(
                  el.childNodes,
                  styleForRuns,
                  pageWidth
                );
                currentParagraphRuns.push(...childRuns);
              } else {
                // 不在"序号+内容"结构中，作为独立段落处理
                const headingParagraph = await this.parseHeading(
                  headingElement
                );
                result.push(headingParagraph);
              }
            } else {
              // 其他情况按原来的逻辑处理
              if (currentParagraphRuns.length === 0) {
                const inheritedStyle = TextProcessor.extractTextStyle(
                  el,
                  parentStyle
                );
                if (
                  inheritedStyle.alignment &&
                  ['left', 'center', 'right', 'justify'].includes(
                    inheritedStyle.alignment
                  )
                ) {
                  currentParagraphAlignment = inheritedStyle.alignment;
                }

                // 检查父元素的对齐方式
                if (
                  !currentParagraphAlignment ||
                  currentParagraphAlignment === 'left'
                ) {
                  let currentEl = el.parentElement;
                  while (currentEl) {
                    const parentStyle = StyleParser.parseStyle(currentEl);
                    if (
                      parentStyle.textAlign &&
                      ['left', 'center', 'right', 'justify'].includes(
                        parentStyle.textAlign
                      )
                    ) {
                      currentParagraphAlignment = parentStyle.textAlign;
                      break;
                    }
                    currentEl = currentEl.parentElement;
                  }
                }
              }

              const styleForRuns = TextProcessor.extractTextStyle(
                el,
                parentStyle
              );
              const childRuns = await this.parseRuns(
                el.childNodes,
                styleForRuns,
                pageWidth
              );

              currentParagraphRuns.push(...childRuns);
            }
          }
        } else {
          // 内联元素处理
          if (currentParagraphRuns.length === 0) {
            const inheritedStyle = TextProcessor.extractTextStyle(
              el,
              parentStyle
            );
            if (
              inheritedStyle.alignment &&
              ['left', 'center', 'right', 'justify'].includes(
                inheritedStyle.alignment
              )
            ) {
              currentParagraphAlignment = inheritedStyle.alignment;
            }

            // 检查父元素的对齐方式
            if (
              !currentParagraphAlignment ||
              currentParagraphAlignment === 'left'
            ) {
              let currentEl = el.parentElement;
              while (currentEl) {
                const parentStyle = StyleParser.parseStyle(currentEl);
                if (
                  parentStyle.textAlign &&
                  ['left', 'center', 'right', 'justify'].includes(
                    parentStyle.textAlign
                  )
                ) {
                  currentParagraphAlignment = parentStyle.textAlign;
                  break;
                }
                currentEl = currentEl.parentElement;
              }
            }
          }

          const styleForRuns = TextProcessor.extractTextStyle(el, parentStyle);
          const childRuns = await this.parseRuns(
            el.childNodes,
            styleForRuns,
            pageWidth
          );

          currentParagraphRuns.push(...childRuns);
        }
      } else if (node.nodeType === this.NODE_TYPES.TEXT_NODE) {
        const text = node.textContent?.trim();
        if (text) {
          currentParagraphRuns.push(new Run({ text }));
        }
      } else if (
        node.nodeType === (this.NODE_TYPES as any).DOCUMENT_FRAGMENT_NODE
      ) {
        const children = await this.parseHtmlNodes(node.childNodes, margins);
        result.push(...children);
      }
    }

    finalizeCurrentParagraph();

    return result;
  }

  /**
   * 解析段落
   */
  private async parseParagraph(el: Element): Promise<Paragraph> {
    const style = TextProcessor.extractTextStyle(el);
    const runs: Run[] = await this.parseRuns(el.childNodes, style);
    let alignment;
    if (style.alignment) {
      if (['left', 'center', 'right', 'justify'].includes(style.alignment)) {
        alignment = style.alignment;
      }
    } else if (el.hasAttribute('align')) {
      const alignAttr = el.getAttribute('align')?.toLowerCase();
      if (['left', 'center', 'right', 'justify'].includes(alignAttr)) {
        alignment = alignAttr;
      }
    }

    // 检查父元素的对齐方式
    if (!alignment) {
      let currentEl = el.parentElement;
      while (currentEl) {
        const parentStyle = StyleParser.parseStyle(currentEl);
        if (
          parentStyle.textAlign &&
          ['left', 'center', 'right', 'justify'].includes(parentStyle.textAlign)
        ) {
          alignment = parentStyle.textAlign;
          break;
        }
        currentEl = currentEl.parentElement;
      }
    }

    return new Paragraph({
      children: runs,
      alignment,
      spacing: { line: 360, after: 0 }, // 减少段落后间距
    });
  }

  /**
   * 解析标题
   */
  private async parseHeading(el: Element): Promise<Paragraph> {
    const parentElement = el.parentElement;
    const parentStyle = parentElement
      ? TextProcessor.extractTextStyle(parentElement, {})
      : {};
    const style = TextProcessor.extractTextStyle(el, parentStyle);
    const runs: Run[] = await this.parseRuns(el.childNodes, style);

    const level = parseInt(el.tagName.substring(1), 10);
    let heading:
      | 'Heading1'
      | 'Heading2'
      | 'Heading3'
      | 'Heading4'
      | 'Heading5'
      | 'Heading6'
      | 'Title' = HeadingLevel.HEADING_1;
    switch (level) {
      case 1:
        heading = HeadingLevel.HEADING_1;
        break;
      case 2:
        heading = HeadingLevel.HEADING_2;
        break;
      case 3:
        heading = HeadingLevel.HEADING_3;
        break;
      case 4:
        heading = HeadingLevel.HEADING_4;
        break;
      case 5:
        heading = HeadingLevel.HEADING_5;
        break;
      case 6:
        heading = HeadingLevel.HEADING_6;
        break;
    }

    // 处理对齐方式：优先使用父元素的text-align样式
    let alignment: any;
    if (parentStyle.alignment) {
      if (
        ['left', 'center', 'right', 'justify'].includes(parentStyle.alignment)
      ) {
        alignment = parentStyle.alignment;
      }
    } else if (style.alignment) {
      if (['left', 'center', 'right', 'justify'].includes(style.alignment)) {
        alignment = style.alignment;
      }
    } else if (el.hasAttribute('align')) {
      const alignAttr = el.getAttribute('align')?.toLowerCase();
      if (['left', 'center', 'right', 'justify'].includes(alignAttr)) {
        alignment = alignAttr;
      }
    }

    // 检查父元素的对齐方式
    if (!alignment) {
      let currentEl = el.parentElement;
      while (currentEl) {
        const parentStyleCheck = StyleParser.parseStyle(currentEl);
        if (
          parentStyleCheck.textAlign &&
          ['left', 'center', 'right', 'justify'].includes(
            parentStyleCheck.textAlign
          )
        ) {
          alignment = parentStyleCheck.textAlign;
          break;
        }
        currentEl = currentEl.parentElement;
      }
    }

    // 检查是否在inline-block容器中，如果是则不添加额外间距
    const isInInlineBlockContainer =
      parentElement &&
      StyleParser.parseStyle(parentElement).display === 'inline-block';

    // 在inline-block容器中的标题不添加任何间距，保持与序号在同一行
    // 其他情况下也减少间距，避免过多空行
    const spacing = isInInlineBlockContainer
      ? { line: 360 }
      : { after: 0, line: 360 }; // 减少标题后间距

    // 转换对齐方式为docx枚举
    const docxAlignment = alignment
      ? this.convertAlignment(alignment)
      : undefined;

    const paragraph = new Paragraph({
      children: runs,
      heading,
      alignment: docxAlignment,
      spacing,
    });

    return paragraph;
  }

  /**
   * 解析列表
   */
  private async parseList(
    el: Element,
    numberingReference: string
  ): Promise<Paragraph[]> {
    const result: Paragraph[] = [];
    for (const li of Array.from(el.querySelectorAll('li'))) {
      const runs = await this.parseRuns(li.childNodes);
      result.push(
        new Paragraph({
          children: runs,
          numbering: { reference: numberingReference, level: 0 },
          spacing: { line: 360 },
        })
      );
    }
    return result;
  }

  /**
   * 解析表格
   */
  private async parseTable(el: Element): Promise<Table> {
    const rows: TableRow[] = [];
    const trs = el.querySelectorAll('tr');
    for (const tr of trs) {
      const cells: TableCell[] = [];
      const tds = tr.querySelectorAll('td,th');
      for (const td of tds) {
        const cellParagraphs = await this.parseHtmlNodes(td.childNodes);
        const style = StyleParser.parseStyle(td);
        const width =
          style.width || parseInt(td.getAttribute('width') || '100', 10);

        let alignment;
        if (
          style.textAlign &&
          ['left', 'center', 'right', 'justify'].includes(style.textAlign)
        ) {
          alignment = style.textAlign;
        } else if (td.hasAttribute('align')) {
          const alignAttr = td.getAttribute('align')?.toLowerCase();
          if (['left', 'center', 'right', 'justify'].includes(alignAttr)) {
            alignment = alignAttr;
          }
        }

        let verticalAlign: 'center' | 'top' | 'bottom' | undefined;
        if (style.verticalAlign) {
          const v = style.verticalAlign.toLowerCase();
          if (v === 'middle' || v === 'center') verticalAlign = 'center';
          else if (v === 'top') verticalAlign = 'top';
          else if (v === 'bottom') verticalAlign = 'bottom';
        } else if (td.hasAttribute('valign')) {
          const valignAttr = td.getAttribute('valign')?.toLowerCase();
          if (valignAttr === 'middle' || valignAttr === 'center')
            verticalAlign = 'center';
          else if (valignAttr === 'top') verticalAlign = 'top';
          else if (valignAttr === 'bottom') verticalAlign = 'bottom';
        }

        const cellProps: any = {
          children: cellParagraphs.map(p => {
            if (p instanceof Paragraph) {
              const paragraphProps: any = {
                children: (p as any)._children || [],
                heading: (p as any).options?.heading,
                spacing: { line: 360 },
              };

              if (alignment) {
                paragraphProps.alignment = alignment;
              }

              if (verticalAlign) {
                paragraphProps.verticalAlign = verticalAlign;
              }

              return new Paragraph(paragraphProps);
            }
            return p;
          }),
          width: { size: width * 20, type: 'dxa' },
        };

        if (verticalAlign) {
          cellProps.verticalAlign = verticalAlign;
        }

        if (alignment) {
          cellProps.alignment = alignment;
        }

        cellProps.borders = {
          top: { style: 'single', size: 1, color: '000000' },
          bottom: { style: 'single', size: 1, color: '000000' },
          left: { style: 'single', size: 1, color: '000000' },
          right: { style: 'single', size: 1, color: '000000' },
        };

        cells.push(new TableCell(cellProps));
      }
      rows.push(new TableRow({ children: cells }));
    }
    return new Table({
      rows,
      width: {
        size: 100,
        type: 'pct',
      },
    });
  }

  /**
   * 解析运行块
   */
  private async parseRuns(
    nodeList: NodeList,
    parentStyle: any = {},
    pageWidth?: number
  ): Promise<Run[]> {
    const runs: Run[] = [];
    for (let i = 0; i < nodeList.length; i++) {
      const node = nodeList[i];
      if (node.nodeType === this.NODE_TYPES.TEXT_NODE) {
        // 使用SpaceProcessor处理空格
        let text = this.spaceProcessor.processTextNodeSpaces(node);

        // 检查是否在SPAN元素中
        const parentElement = node.parentElement;
        const isInSpan = parentElement && parentElement.tagName === 'SPAN';

        // 对于非SPAN元素，去除首尾空格
        if (!isInSpan) {
          text = text.trim();
        } else {
          // 对于SPAN元素，特殊处理序号空格（数字序号或字母序号）
          const isNumberSpan =
            text.trim().match(/^\d+\.\s*$/) ||
            text.trim().match(/^[A-Za-z]、\s*$/);
          if (isNumberSpan) {
            // 序号SPAN：保留一个前导空格和后导空格
            text = ` ${text.trim()} `;
          }
        }

        if (text) {
          let currentColor = parentStyle.color;
          let currentEmphasis = parentStyle.emphasisMark;
          let currentNode = node.parentNode as Element;
          while (
            currentNode &&
            currentNode.nodeType === this.NODE_TYPES.ELEMENT_NODE
          ) {
            const elementStyle = StyleParser.parseStyle(currentNode);
            if (elementStyle.color !== undefined) {
              currentColor = elementStyle.color;
            }
            if (
              elementStyle.textEmphasis !== undefined ||
              elementStyle.textEmphasisPosition !== undefined
            ) {
              currentEmphasis = StyleParser.mapTextEmphasisToDocx(
                elementStyle.textEmphasis,
                elementStyle.textEmphasisPosition
              );
            }
            if (
              elementStyle.color !== undefined &&
              (elementStyle.textEmphasis !== undefined ||
                elementStyle.textEmphasisPosition !== undefined)
            )
              break;
            if (
              elementStyle.color !== undefined &&
              parentStyle.color === undefined
            )
              break;
            if (
              (elementStyle.textEmphasis !== undefined ||
                elementStyle.textEmphasisPosition !== undefined) &&
              parentStyle.emphasisMark === undefined
            )
              break;

            currentNode = currentNode.parentNode as Element;
          }

          const runProps: any = {
            text,
            bold: parentStyle.bold,
            italics: parentStyle.italics,
            underline: parentStyle.underline,
            color: currentColor !== undefined ? currentColor : '000000',
            emphasisMark: currentEmphasis,
          };

          // 添加字体大小
          if (parentStyle.fontSize) {
            runProps.size = parentStyle.fontSize;
          }

          const run = new Run(runProps);
          runs.push(run);
        } else {
          const rawText = node.textContent ?? '';
          const pure = rawText.replace(/[\s\u3000]/g, '');
          if (rawText && pure.length === 0) {
            const parentTags = TextProcessor.getParentTagNamesUntilDiv(node);
            if (parentTags.length && parentTags.includes('U')) {
              runs.push(
                new Run({ text: rawText, underline: { type: 'single' } })
              );
              continue;
            }
          }
        }
      } else if (node.nodeType === this.NODE_TYPES.ELEMENT_NODE) {
        const el = node as Element;

        // TODO: 完全跳过绝对定位的元素，这些元素在浏览器中"漂浮"，不影响正常布局
        // 包括其中的BR标签也不处理，因为它们在浏览器中不可见
        const style = StyleParser.parseStyle(el);
        if (style.position === 'absolute') {
          continue; // 完全跳过绝对定位的元素
        }

        // 跳过MSO注释和条件注释
        if (
          el.tagName === 'COMMENT' ||
          (el.textContent && el.textContent.includes('[if !supportLists]'))
        ) {
          continue;
        }

        // 处理换行标签
        if (el.tagName === 'BR') {
          runs.push(new Run({ break: 1 }));
          continue;
        }

        if (el.tagName === 'IMG') {
          const style = StyleParser.parseStyle(el);
          const src = el.getAttribute('src');
          if (src && src.startsWith('data:image/')) {
            if (src.startsWith('data:image/svg+xml')) {
              let width =
                style.width || parseInt(el.getAttribute('width') || '0', 10);
              let height =
                style.height || parseInt(el.getAttribute('height') || '0', 10);
              if (!width || !height) {
                let svgText = '';
                if (/;base64,/.test(src)) {
                  const base64 = src.split(';base64,').pop()!;
                  svgText = Buffer.from(base64, 'base64').toString();
                } else {
                  svgText = decodeURIComponent(src.split(',').pop()!);
                }
                const size = ImageProcessor.getSvgIntrinsicSize(svgText);
                if (size) {
                  if (!width) width = size.width;
                  if (!height) height = size.height;
                }
              }
              if (!width) width = 120;
              if (!height) height = 40;

              if (pageWidth && width > pageWidth) {
                const ratio = pageWidth / width;
                width = Math.round(width * ratio);
                height = Math.round(height * ratio);
              }

              const buffer = await ImageProcessor.svgBase64ToPngBuffer(
                src,
                width,
                height
              );

              const imageRunProps: any = {
                data: buffer,
                transformation: { width, height },
                type: 'png',
                verticalAlign: 'middle',
              };

              const parentElement = el.parentElement;

              if (parentElement) {
                const parentStyle = StyleParser.parseStyle(parentElement);
                if (parentStyle.display === 'inline-block') {
                  if (parentStyle.margin) {
                    const margins = parentStyle.margin.split(' ');
                    if (margins.length === 4) {
                      imageRunProps.margins = {
                        top: parseInt(margins[0]),
                        right: parseInt(margins[1]),
                        bottom: parseInt(margins[2]),
                        left: parseInt(margins[3]),
                      };
                    }
                  }
                }
              }

              runs.push(new ImageRun(imageRunProps));
            } else {
              const base64Data = src.split(';base64,').pop();
              if (base64Data) {
                const buffer = Buffer.from(base64Data, 'base64');

                let width =
                  style.width || parseInt(el.getAttribute('width') || '0', 10);
                let height =
                  style.height ||
                  parseInt(el.getAttribute('height') || '0', 10);

                if (width <= 0 || height <= 0) {
                  try {
                    const metadata = await sharp(buffer).metadata();
                    width = metadata.width || width;
                    height = metadata.height || height;
                  } catch (e) {
                    if (width <= 0) width = 100;
                    if (height <= 0) height = 100;
                  }
                }

                if (pageWidth && width > pageWidth) {
                  const ratio = pageWidth / width;
                  width = Math.round(width * ratio);
                  height = Math.round(height * ratio);
                }

                const imageRunProps: any = {
                  data: buffer,
                  transformation: { width, height },
                  type: 'png',
                  verticalAlign: 'middle',
                };

                const parentElement = el.parentElement;

                if (parentElement) {
                  const parentStyle = StyleParser.parseStyle(parentElement);
                  if (parentStyle.display === 'inline-block') {
                    if (parentStyle.margin) {
                      const margins = parentStyle.margin.split(' ');
                      if (margins.length === 4) {
                        imageRunProps.margins = {
                          top: parseInt(margins[0]),
                          right: parseInt(margins[1]),
                          bottom: parseInt(margins[2]),
                          left: parseInt(margins[3]),
                        };
                      }
                    }
                  }
                }

                runs.push(new ImageRun(imageRunProps));
              }
            }
          }
        } else {
          const style = TextProcessor.extractTextStyle(el, parentStyle);
          const styleForRuns = { ...style, alignment: undefined };
          const childRuns = await this.parseRuns(
            el.childNodes,
            styleForRuns,
            pageWidth
          );

          // 使用新的换行处理器
          const shouldAddBreakBefore =
            LineBreakProcessor.shouldAddLineBreakBefore(el);
          const shouldAddBreakAfter =
            LineBreakProcessor.shouldAddLineBreakAfter(el);

          if (shouldAddBreakBefore && childRuns.length > 0) {
            runs.push(new Run({ break: 1 }));
          }

          runs.push(...childRuns);

          if (shouldAddBreakAfter && childRuns.length > 0) {
            runs.push(new Run({ break: 1 }));
          }
        }
      }
    }
    return runs;
  }

  /**
   * 计算页面宽度
   */
  private calculatePageWidth(margins: {
    left?: number;
    right?: number;
  }): number {
    const DEFAULT_MARGINS = {
      left: 1440,
      right: 1440,
    };
    const DEFAULT_PAGE_WIDTH_CM = 21;
    const CM_TO_PX = 37.8;

    const leftMargin = margins.left ?? DEFAULT_MARGINS.left;
    const rightMargin = margins.right ?? DEFAULT_MARGINS.right;
    const marginCm = (leftMargin + rightMargin) / 567;
    const maxWidthCm = DEFAULT_PAGE_WIDTH_CM - marginCm;
    return maxWidthCm * CM_TO_PX;
  }
}
